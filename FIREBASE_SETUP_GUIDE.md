# دليل إعداد Firebase للموقع

## الخطوة 1: إنشاء مشروع Firebase

1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. انقر على "Add project" أو "إضافة مشروع"
3. أدخل اسم المشروع (مثل: "yazan-store")
4. اختر إعدادات Google Analytics (يمكنك تعطيلها للبساطة)
5. انقر على "Create project"

## الخطوة 2: إعداد Firestore Database

1. في لوحة تحكم Firebase، انقر على "Firestore Database"
2. انقر على "Create database"
3. اختر "Start in test mode" (للتطوير)
4. اختر موقع قاعدة البيانات (اختر الأقرب لك)
5. انقر على "Done"

## الخطوة 3: إعداد Web App

1. في لوحة تحكم Firebase، انقر على أيقونة الويب `</>`
2. أدخل اسم التطبيق (مثل: "yazan-store-web")
3. لا تحتاج لتفعيل Firebase Hosting الآن
4. انقر على "Register app"
5. ستظهر لك معلومات التكوين - احتفظ بها

## الخطوة 4: تحديث ملف firebase-config.js

1. افتح ملف `js/firebase-config.js`
2. استبدل القيم التالية بالقيم من Firebase Console:

```javascript
const firebaseConfig = {
    apiKey: "your-actual-api-key",
    authDomain: "your-project-id.firebaseapp.com",
    projectId: "your-actual-project-id",
    storageBucket: "your-project-id.appspot.com",
    messagingSenderId: "your-actual-sender-id",
    appId: "your-actual-app-id"
};
```

## الخطوة 5: إعداد قواعد الأمان (اختياري للتطوير)

في Firestore Database، اذهب إلى "Rules" وضع هذه القواعد للتطوير:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if true;
    }
  }
}
```

**تحذير:** هذه القواعد تسمح بالقراءة والكتابة للجميع. استخدمها فقط للتطوير.

## الخطوة 6: اختبار النظام

1. افتح الموقع في المتصفح
2. اذهب إلى صفحة الإدارة (admin-products.html)
3. أضف منتج جديد
4. افتح الموقع في متصفح آخر أو جهاز آخر
5. يجب أن ترى المنتج الجديد يظهر تلقائياً

## المميزات الجديدة:

### 1. التزامن التلقائي
- المنتجات تظهر على جميع الأجهزة فوراً
- لا حاجة لتحديث الصفحة يدوياً

### 2. النسخ الاحتياطي
- البيانات محفوظة في Firebase وlocalStorage
- إذا فشل Firebase، يعمل النظام بـ localStorage

### 3. التحديث التلقائي
- الصفحات تتحدث تلقائياً عند إضافة منتجات جديدة
- لا حاجة لإعادة تحميل الصفحة

## استكشاف الأخطاء:

### إذا لم تظهر المنتجات:
1. تأكد من صحة إعدادات Firebase في `firebase-config.js`
2. افتح Developer Tools وتحقق من وجود أخطاء في Console
3. تأكد من أن قواعد Firestore تسمح بالقراءة والكتابة

### إذا لم يعمل التحديث التلقائي:
1. تأكد من أن Firebase SDK محمل بشكل صحيح
2. تحقق من Console للتأكد من عدم وجود أخطاء JavaScript

## للإنتاج:

عندما تكون جاهزاً للنشر:
1. قم بتحديث قواعد Firestore لتكون أكثر أماناً
2. أضف نظام مصادقة للمشرفين
3. قم بتفعيل Firebase Hosting لاستضافة الموقع

## الدعم:

إذا واجهت أي مشاكل، تحقق من:
- [Firebase Documentation](https://firebase.google.com/docs)
- [Firestore Getting Started](https://firebase.google.com/docs/firestore/quickstart)
