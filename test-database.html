<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام قاعدة البيانات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .product-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
        }
        .product-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    </style>
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore-compat.js"></script>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار نظام قاعدة البيانات</h1>
        <p>هذه الصفحة لاختبار عمل نظام قاعدة البيانات الجديد</p>
        
        <div id="connectionStatus" class="status info">
            🔄 جاري فحص الاتصال...
        </div>
    </div>

    <div class="container">
        <h2>🔧 أدوات الاختبار</h2>
        <button onclick="testConnection()">اختبار الاتصال</button>
        <button onclick="addTestProduct()">إضافة منتج تجريبي</button>
        <button onclick="loadProducts()">تحميل المنتجات</button>
        <button onclick="clearLocalStorage()">مسح localStorage</button>
        <button onclick="testRealTimeUpdates()">اختبار التحديث التلقائي</button>
    </div>

    <div class="container">
        <h2>📊 حالة النظام</h2>
        <div id="systemStatus"></div>
    </div>

    <div class="container">
        <h2>📦 المنتجات المحملة</h2>
        <div id="productCount" class="status info">عدد المنتجات: 0</div>
        <div id="productsList" class="product-list"></div>
    </div>

    <div class="container">
        <h2>📝 سجل الأحداث</h2>
        <div id="logContainer" style="max-height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 5px;"></div>
        <button onclick="clearLog()">مسح السجل</button>
    </div>

    <!-- Load our database system -->
    <script src="js/firebase-config.js"></script>
    <script src="js/database-manager.js"></script>

    <script>
        let logContainer;
        let productsList;
        let productCount;
        let systemStatus;
        let connectionStatus;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            logContainer = document.getElementById('logContainer');
            productsList = document.getElementById('productsList');
            productCount = document.getElementById('productCount');
            systemStatus = document.getElementById('systemStatus');
            connectionStatus = document.getElementById('connectionStatus');
            
            log('🚀 صفحة الاختبار جاهزة');
            
            // Wait a bit for database manager to initialize
            setTimeout(() => {
                checkSystemStatus();
                loadProducts();
            }, 1000);
        });

        function log(message) {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<strong>${timestamp}:</strong> ${message}`;
            logEntry.style.marginBottom = '5px';
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            logContainer.innerHTML = '';
        }

        function checkSystemStatus() {
            const status = [];
            
            // Check Firebase availability
            if (window.firebaseConfig?.isFirebaseAvailable) {
                status.push('<div class="status success">✅ Firebase متصل ويعمل</div>');
                connectionStatus.innerHTML = '✅ متصل بـ Firebase';
                connectionStatus.className = 'status success';
            } else {
                status.push('<div class="status warning">⚠️ Firebase غير متاح - يعمل بـ localStorage</div>');
                connectionStatus.innerHTML = '⚠️ غير متصل - localStorage فقط';
                connectionStatus.className = 'status warning';
            }
            
            // Check database manager
            if (window.databaseManager) {
                status.push('<div class="status success">✅ Database Manager جاهز</div>');
            } else {
                status.push('<div class="status error">❌ Database Manager غير متاح</div>');
            }
            
            // Check localStorage
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                status.push('<div class="status success">✅ localStorage يعمل</div>');
            } catch (e) {
                status.push('<div class="status error">❌ localStorage لا يعمل</div>');
            }
            
            systemStatus.innerHTML = status.join('');
            log('🔍 تم فحص حالة النظام');
        }

        async function testConnection() {
            log('🔄 اختبار الاتصال...');
            
            if (!window.databaseManager) {
                log('❌ Database Manager غير متاح');
                return;
            }
            
            try {
                const products = await window.databaseManager.getAllProducts();
                log(`✅ تم تحميل ${products.length} منتج بنجاح`);
                
                if (window.databaseManager.isOnline()) {
                    log('🌐 الاتصال بالإنترنت متاح');
                } else {
                    log('📱 وضع عدم الاتصال - localStorage فقط');
                }
                
            } catch (error) {
                log(`❌ خطأ في الاتصال: ${error.message}`);
            }
        }

        async function addTestProduct() {
            log('➕ إضافة منتج تجريبي...');
            
            if (!window.databaseManager) {
                log('❌ Database Manager غير متاح');
                return;
            }
            
            const testProduct = {
                id: Date.now(),
                name: `منتج تجريبي ${new Date().toLocaleTimeString('ar-SA')}`,
                brand: 'Test Brand',
                description: 'هذا منتج تجريبي لاختبار النظام',
                category: 'test',
                status: 'active',
                price: 99.99,
                originalPrice: 129.99,
                stock: 10,
                images: ['https://via.placeholder.com/400x500?text=Test+Product'],
                sizes: ['M', 'L'],
                colors: ['أزرق', 'أحمر'],
                showOnHomepage: true,
                showOnProducts: true,
                showOnSale: false
            };
            
            try {
                const result = await window.databaseManager.addProduct(testProduct);
                if (result.success) {
                    log(`✅ تم إضافة المنتج بنجاح (ID: ${result.id})`);
                    loadProducts();
                } else {
                    log(`❌ فشل في إضافة المنتج: ${result.error}`);
                }
            } catch (error) {
                log(`❌ خطأ في إضافة المنتج: ${error.message}`);
            }
        }

        async function loadProducts() {
            log('📦 تحميل المنتجات...');
            
            if (!window.databaseManager) {
                log('❌ Database Manager غير متاح');
                return;
            }
            
            try {
                const products = await window.databaseManager.getAllProducts();
                
                productCount.innerHTML = `عدد المنتجات: ${products.length}`;
                productCount.className = products.length > 0 ? 'status success' : 'status info';
                
                productsList.innerHTML = '';
                
                if (products.length === 0) {
                    productsList.innerHTML = '<div class="status info">لا توجد منتجات</div>';
                } else {
                    products.forEach(product => {
                        const productDiv = document.createElement('div');
                        productDiv.className = 'product-item';
                        productDiv.innerHTML = `
                            <div>
                                <strong>${product.name}</strong><br>
                                <small>السعر: $${product.price} | الفئة: ${product.category}</small>
                            </div>
                            <div>
                                <button onclick="deleteProduct('${product.id}')" style="background-color: #dc3545;">حذف</button>
                            </div>
                        `;
                        productsList.appendChild(productDiv);
                    });
                }
                
                log(`📦 تم تحميل ${products.length} منتج`);
                
            } catch (error) {
                log(`❌ خطأ في تحميل المنتجات: ${error.message}`);
            }
        }

        async function deleteProduct(productId) {
            log(`🗑️ حذف المنتج ${productId}...`);
            
            try {
                const result = await window.databaseManager.deleteProduct(productId);
                if (result.success) {
                    log(`✅ تم حذف المنتج بنجاح`);
                    loadProducts();
                } else {
                    log(`❌ فشل في حذف المنتج: ${result.error}`);
                }
            } catch (error) {
                log(`❌ خطأ في حذف المنتج: ${error.message}`);
            }
        }

        function clearLocalStorage() {
            localStorage.removeItem('vaith_products');
            log('🧹 تم مسح localStorage');
            loadProducts();
        }

        function testRealTimeUpdates() {
            log('⚡ اختبار التحديث التلقائي...');
            
            if (!window.databaseManager) {
                log('❌ Database Manager غير متاح');
                return;
            }
            
            // Set up real-time listener
            const unsubscribe = window.databaseManager.onProductsChange((products) => {
                log(`🔄 تحديث تلقائي: ${products.length} منتج`);
                
                productCount.innerHTML = `عدد المنتجات: ${products.length} (تحديث تلقائي)`;
                productCount.className = 'status success';
                
                // Auto-refresh the products list
                setTimeout(loadProducts, 500);
            });
            
            log('✅ تم تفعيل التحديث التلقائي');
            log('💡 جرب إضافة منتج من صفحة أخرى لرؤية التحديث التلقائي');
            
            // Store unsubscribe function
            window.testUnsubscribe = unsubscribe;
        }
    </script>
</body>
</html>
