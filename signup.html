<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - The Project Faith</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            padding-top: 80px;
            background: linear-gradient(135deg, #4B0082 0%, #D8BFD8 100%);
            min-height: 100vh;
            transition: background 0.3s ease;
        }

        /* Dark mode background for signup page */
        [data-theme="dark"] body,
        body.dark-theme {
            background: linear-gradient(135deg, #0f0f0f 0%, #18181b 25%, #27272a 50%, #3f3f46 75%, #52525b 100%);
        }
        
        .auth-container {
            max-width: 450px;
            margin: 2rem auto;
            background: var(--card-bg);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }

        /* Dark mode auth container */
        [data-theme="dark"] .auth-container,
        body.dark-theme .auth-container {
            background: var(--card-bg);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
            border: 1px solid var(--border-color);
        }
        
        .auth-header {
            background: linear-gradient(135deg, #4B0082, #6a1b9a);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .auth-header h1 {
            margin-bottom: 0.5rem;
            font-size: 2rem;
        }
        
        .auth-header p {
            opacity: 0.9;
        }
        
        .auth-form {
            padding: 2rem;
            background: var(--card-bg);
            transition: background-color 0.3s ease;
        }

        .form-row {
            display: flex;
            gap: 1rem;
        }

        @media (max-width: 480px) {
            .form-row {
                flex-direction: column;
                gap: 0;
            }
        }

        .form-group {
            margin-bottom: var(--form-element-margin);
            flex: 1;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--text-color);
            transition: color 0.3s ease;
        }

        .form-group input {
            width: 100%;
            padding: var(--form-input-padding);
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 1rem;
            background: var(--input-bg);
            color: var(--text-color);
            transition: all 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(75, 0, 130, 0.2);
        }
        
        .form-group.error input {
            border-color: #e74c3c;
        }
        
        .error-message {
            color: #e74c3c;
            font-size: 0.9rem;
            margin-top: 0.5rem;
            display: none;
        }
        
        .form-group.error .error-message {
            display: block;
        }

        .password-input-wrapper {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--text-color);
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.3s ease;
            opacity: 0.6;
        }

        .password-toggle:hover {
            opacity: 1;
            background: rgba(75, 0, 130, 0.1);
        }

        .password-toggle i {
            font-size: 1rem;
        }
        
        .password-strength {
            margin-top: 0.5rem;
            font-size: 0.9rem;
            color: var(--text-light);
            transition: color 0.3s ease;
        }

        .strength-bar {
            height: 4px;
            background: var(--border-color);
            border-radius: 2px;
            margin: 0.5rem 0;
            overflow: hidden;
            transition: background-color 0.3s ease;
        }

        .strength-fill {
            height: 100%;
            transition: width 0.3s ease, background-color 0.3s ease;
            width: 0%;
            background: #e74c3c;
        }

        .strength-fill.weak { background: #e74c3c; }
        .strength-fill.medium { background: #f39c12; }
        .strength-fill.strong { background: #27ae60; }
        
        .checkbox-group {
            display: flex;
            align-items: flex-start;
            gap: 0.5rem;
            margin-bottom: var(--form-element-margin);
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin-top: 0.2rem;
        }

        .checkbox-group label {
            margin-bottom: 0;
            font-size: 0.9rem;
            line-height: 1.4;
            color: var(--text-color);
            transition: color 0.3s ease;
        }

        .checkbox-group a {
            color: var(--primary-color);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .checkbox-group a:hover {
            text-decoration: underline;
            color: #6a1b9a;
        }

        .checkbox-group.error {
            border: 1px solid #e74c3c;
            border-radius: 4px;
            padding: 0.5rem;
            background: rgba(231, 76, 60, 0.05);
        }
        
        .signup-btn {
            width: 100%;
            background: linear-gradient(135deg, #4B0082, #6a1b9a);
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .signup-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(75, 0, 130, 0.3);
        }
        
        .signup-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .divider {
            text-align: center;
            margin: 1.5rem 0;
            position: relative;
            color: var(--text-light);
            transition: color 0.3s ease;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--border-color);
            transition: background-color 0.3s ease;
        }

        .divider span {
            background: var(--card-bg);
            padding: 0 1rem;
            transition: background-color 0.3s ease;
        }

        .social-login {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .social-btn {
            flex: 1;
            padding: 10px;
            border: 2px solid var(--border-color);
            background: var(--card-bg);
            color: var(--text-color);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .social-btn:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
            background: var(--section-bg);
        }
        
        .auth-footer {
            text-align: center;
            padding: 1rem 2rem 2rem;
            color: var(--text-light);
            background: var(--card-bg);
            transition: all 0.3s ease;
        }

        .auth-footer a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .auth-footer a:hover {
            text-decoration: underline;
            color: #6a1b9a;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin: 1rem 0;
        }
        
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #4B0082;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-left">
                <button class="mobile-menu-btn" id="mobileMenuBtn">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <a href="index.html">
                        <svg width="150" height="40" viewBox="0 0 150 40" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#4B0082;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#D8BFD8;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            <text x="5" y="28" font-family="Inter, sans-serif" font-size="16" font-weight="700" fill="url(#logoGradient)">The Project Faith</text>
                        </svg>
                    </a>
                </div>
            </div>
            
            <div class="nav-center">
                <ul class="nav-links" id="navLinks">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="products.html">Products</a></li>
                    <li><a href="sale.html">Sale</a></li>
                </ul>
                <div class="search-box">
                    <input type="text" placeholder="Search for products..." id="searchInput">
                    <button class="search-btn"><i class="fas fa-search"></i></button>
                </div>
            </div>

            <div class="nav-right">
                <div class="nav-icons">
                    <button class="nav-icon theme-toggle" id="themeToggle" title="Toggle dark mode">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </button>
                    <a href="login.html" class="nav-icon">
                        <i class="fas fa-user"></i>
                    </a>
                    <button class="nav-icon" id="favoritesBtn">
                        <i class="fas fa-heart"></i>
                        <span class="badge" id="favoritesCount">0</span>
                    </button>
                    <button class="nav-icon" id="cartBtn">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="badge" id="cartCount">0</span>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Signup Form -->
    <div class="auth-container">
        <div class="auth-header">
            <h1>Join The Project Faith</h1>
            <p>Create your account and start shopping</p>
        </div>
        
        <form class="auth-form" id="signupForm">
            <div class="form-row">
                <div class="form-group">
                    <label for="firstName">First Name</label>
                    <input type="text" id="firstName" name="firstName" required>
                    <div class="error-message" id="firstNameError"></div>
                </div>
                
                <div class="form-group">
                    <label for="lastName">Last Name</label>
                    <input type="text" id="lastName" name="lastName" required>
                    <div class="error-message" id="lastNameError"></div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="email">Email Address</label>
                <input type="email" id="email" name="email" required>
                <div class="error-message" id="emailError"></div>
            </div>
            
            <div class="form-group">
                <label for="password">Password</label>
                <div class="password-input-wrapper">
                    <input type="password" id="password" name="password" required>
                    <button type="button" class="password-toggle" onclick="togglePassword('password')">
                        <i class="fas fa-eye" id="password-toggle-icon"></i>
                    </button>
                </div>
                <div class="password-strength">
                    <div class="strength-bar">
                        <div class="strength-fill" id="strengthFill"></div>
                    </div>
                    <span id="strengthText">Password strength</span>
                </div>
                <div class="error-message" id="passwordError"></div>
            </div>
            
            <div class="form-group">
                <label for="confirmPassword">Confirm Password</label>
                <div class="password-input-wrapper">
                    <input type="password" id="confirmPassword" name="confirmPassword" required>
                    <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')">
                        <i class="fas fa-eye" id="confirmPassword-toggle-icon"></i>
                    </button>
                </div>
                <div class="error-message" id="confirmPasswordError"></div>
            </div>
            
            <div class="checkbox-group">
                <input type="checkbox" id="terms" name="terms" required>
                <label for="terms">
                    I agree to the <a href="#">Terms of Service</a> and
                    <a href="#">Privacy Policy</a>
                </label>
            </div>
            
            <div class="checkbox-group">
                <input type="checkbox" id="newsletter" name="newsletter">
                <label for="newsletter">
                    Subscribe to our newsletter for exclusive offers and updates
                </label>
            </div>
            
            <button type="submit" class="signup-btn" id="signupBtn">
                Create Account
            </button>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Creating your account...</p>
            </div>
        </form>
        
        <div class="divider">
            <span>or sign up with</span>
        </div>
        
        <div class="social-login">
            <button class="social-btn" id="googleSignup">
                <i class="fab fa-google"></i>
                Google
            </button>
            <button class="social-btn" id="facebookSignup">
                <i class="fab fa-facebook-f"></i>
                Facebook
            </button>
        </div>
        
        <div class="auth-footer">
            <p>Already have an account? <a href="login.html">Sign in here</a></p>
        </div>
    </div>

    <!-- Overlay -->
    <div class="overlay" id="overlay"></div>

    <!-- Scripts -->
    <script src="js/theme-toggle.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/store-status.js"></script>
    <script src="js/main.js"></script>
    <script src="js/cart.js"></script>
    <script src="js/favorites.js"></script>
    <script src="js/validation.js"></script>

    <script>
        // Initialize auth manager globally
        let authManager;

        // Initialize as soon as auth.js is loaded
        try {
            if (typeof AuthManager !== 'undefined') {
                authManager = new AuthManager();
                console.log('Auth manager initialized globally');
                console.log('Auth manager type:', typeof authManager);
                console.log('Auth manager methods:', Object.getOwnPropertyNames(Object.getPrototypeOf(authManager)));
            } else {
                console.error('AuthManager class not found');
            }
        } catch (error) {
            console.error('Error initializing auth manager:', error);
        }
    </script>
    
    <script>
        // Password strength checker
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strengthFill = document.getElementById('strengthFill');
            const strengthText = document.getElementById('strengthText');
            
            const strength = calculatePasswordStrength(password);
            
            strengthFill.style.width = strength.percentage + '%';
            strengthFill.className = 'strength-fill ' + strength.level;
            strengthText.textContent = strength.text;
        });
        
        function calculatePasswordStrength(password) {
            let score = 0;
            
            if (password.length >= 8) score += 25;
            if (password.match(/[a-z]/)) score += 25;
            if (password.match(/[A-Z]/)) score += 25;
            if (password.match(/[0-9]/)) score += 25;
            if (password.match(/[^a-zA-Z0-9]/)) score += 25;
            
            if (score <= 25) {
                return { percentage: 25, level: 'weak', text: 'Weak password' };
            } else if (score <= 50) {
                return { percentage: 50, level: 'medium', text: 'Medium password' };
            } else if (score <= 75) {
                return { percentage: 75, level: 'strong', text: 'Strong password' };
            } else {
                return { percentage: 100, level: 'strong', text: 'Very strong password' };
            }
        }
        
        // Initialize auth manager and check if user is already logged in
        function initializeAuthManager() {
            return new Promise((resolve, reject) => {
                try {
                    if (typeof AuthManager === 'undefined') {
                        reject(new Error('AuthManager class not found'));
                        return;
                    }

                    if (!authManager) {
                        authManager = new AuthManager();
                        console.log('Auth manager created successfully');
                    }

                    console.log('Auth manager ready:', typeof authManager);
                    console.log('Total users in system:', authManager.getAllUsers().length);

                    resolve(authManager);
                } catch (error) {
                    console.error('Error initializing auth manager:', error);
                    reject(error);
                }
            });
        }

        document.addEventListener('DOMContentLoaded', async function() {
            console.log('DOM loaded, initializing auth manager...'); // Debug log

            try {
                await initializeAuthManager();

                if (authManager.isLoggedIn()) {
                    const user = authManager.getCurrentUser();
                    console.log('User already logged in:', user); // Debug log
                    if (user.role === 'admin') {
                        window.location.href = 'admin-dashboard.html';
                    } else {
                        window.location.href = 'user-profile.html';
                    }
                    return;
                }

                console.log('User not logged in, ready for signup'); // Debug log
            } catch (error) {
                console.error('Failed to initialize auth manager:', error);
                showNotification('System initialization failed. Please refresh the page.', 'error');
            }
        });

        // Signup form functionality
        document.getElementById('signupForm').addEventListener('submit', function(e) {
            e.preventDefault();

            console.log('Signup form submitted'); // Debug log

            const formData = {
                firstName: document.getElementById('firstName').value,
                lastName: document.getElementById('lastName').value,
                email: document.getElementById('email').value,
                password: document.getElementById('password').value,
                confirmPassword: document.getElementById('confirmPassword').value,
                terms: document.getElementById('terms').checked,
                newsletter: document.getElementById('newsletter').checked
            };

            console.log('Form data:', formData); // Debug log

            // Clear previous errors
            clearErrors();

            // Validate form
            if (!validateSignupForm(formData)) {
                console.log('Form validation failed'); // Debug log
                return;
            }

            console.log('Form validation passed'); // Debug log

            // Show loading state
            showLoading(true);

            // Attempt signup
            setTimeout(async () => {
                try {
                    console.log('Attempting signup with authManager:', typeof authManager); // Debug log

                    // Ensure auth manager is available
                    if (typeof authManager === 'undefined' || !authManager) {
                        console.log('Auth manager not available, attempting to create...'); // Debug log
                        if (typeof AuthManager !== 'undefined') {
                            authManager = new AuthManager();
                            console.log('Auth manager created successfully'); // Debug log
                        } else {
                            throw new Error('AuthManager class not found');
                        }
                    }

                    const user = await authManager.signup({
                        firstName: formData.firstName,
                        lastName: formData.lastName,
                        email: formData.email,
                        password: formData.password,
                        newsletter: formData.newsletter
                    });

                    console.log('Signup successful:', user); // Debug log

                    showNotification('Account created successfully! Welcome to The Project Faith!');

                    // Redirect based on user role
                    setTimeout(() => {
                        if (user.role === 'admin') {
                            window.location.href = 'admin-dashboard.html';
                        } else {
                            window.location.href = 'user-profile.html';
                        }
                    }, 1000);

                } catch (error) {
                    console.error('Signup error:', error); // Debug log
                    showError(error.message);
                } finally {
                    showLoading(false);
                }
            }, 1000);
        });
        
        function validateSignupForm(data) {
            let isValid = true;

            if (!data.firstName.trim()) {
                showFieldError('firstName', 'First name is required');
                isValid = false;
            }

            if (!data.lastName.trim()) {
                showFieldError('lastName', 'Last name is required');
                isValid = false;
            }

            if (!data.email || !isValidEmail(data.email)) {
                showFieldError('email', 'Please enter a valid email address');
                isValid = false;
            }

            if (!data.password || data.password.length < 6) {
                showFieldError('password', 'Password must be at least 6 characters');
                isValid = false;
            }

            if (data.password !== data.confirmPassword) {
                showFieldError('confirmPassword', 'Passwords do not match');
                isValid = false;
            }

            if (!data.terms) {
                showFieldError('terms', 'Please accept the Terms of Service and Privacy Policy');
                isValid = false;
            }

            return isValid;
        }
        
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
        
        function showFieldError(fieldName, message) {
            const field = document.getElementById(fieldName);

            if (fieldName === 'terms') {
                // Handle checkbox error differently
                const checkboxGroup = field.closest('.checkbox-group');
                checkboxGroup.classList.add('error');
                showNotification(message, 'error');
            } else {
                const errorElement = document.getElementById(fieldName + 'Error');
                field.parentElement.classList.add('error');
                if (errorElement) {
                    errorElement.textContent = message;
                }
            }
        }
        
        function clearErrors() {
            const errorGroups = document.querySelectorAll('.form-group.error');
            errorGroups.forEach(group => {
                group.classList.remove('error');
                const errorMessage = group.querySelector('.error-message');
                if (errorMessage) {
                    errorMessage.textContent = '';
                }
            });

            // Also clear checkbox group errors
            const checkboxGroups = document.querySelectorAll('.checkbox-group');
            checkboxGroups.forEach(group => {
                group.classList.remove('error');
            });
        }

        function showError(message) {
            showNotification(message, 'error');
        }

        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#17a2b8'};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                z-index: 10000;
                font-weight: 500;
                max-width: 300px;
                word-wrap: break-word;
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Remove after 4 seconds
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }

        function showLoading(show) {
            const loading = document.getElementById('loading');
            const signupBtn = document.getElementById('signupBtn');

            if (show) {
                loading.style.display = 'block';
                signupBtn.disabled = true;
            } else {
                loading.style.display = 'none';
                signupBtn.disabled = false;
            }
        }
        
        // Social signup handlers
        document.getElementById('googleSignup').addEventListener('click', function() {
            showNotification('Google signup would be implemented here');
        });
        
        document.getElementById('facebookSignup').addEventListener('click', function() {
            showNotification('Facebook signup would be implemented here');
        });

        function togglePassword(inputId) {
            const passwordInput = document.getElementById(inputId);
            const toggleIcon = document.getElementById(inputId + '-toggle-icon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }
    </script>
</body>
</html>
