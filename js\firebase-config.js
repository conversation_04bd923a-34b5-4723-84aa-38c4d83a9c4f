// Firebase Configuration
// Replace these values with your actual Firebase project configuration
const firebaseConfig = {
    apiKey: "your-api-key-here",
    authDomain: "your-project-id.firebaseapp.com",
    projectId: "your-project-id",
    storageBucket: "your-project-id.appspot.com",
    messagingSenderId: "your-sender-id",
    appId: "your-app-id"
};

// Initialize Firebase
let app, db;
let isFirebaseAvailable = false;

// Check if Firebase is available
if (typeof firebase !== 'undefined') {
    try {
        // Initialize Firebase
        app = firebase.initializeApp(firebaseConfig);
        db = firebase.firestore();
        isFirebaseAvailable = true;
        console.log('Firebase initialized successfully');
    } catch (error) {
        console.error('Error initializing Firebase:', error);
        isFirebaseAvailable = false;
    }
} else {
    console.warn('Firebase SDK not loaded. Using localStorage as fallback.');
    isFirebaseAvailable = false;
}

// Export configuration
window.firebaseConfig = {
    app,
    db,
    isFirebaseAvailable
};
