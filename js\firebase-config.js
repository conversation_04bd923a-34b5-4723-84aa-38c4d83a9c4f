// Firebase Configuration
// Your actual Firebase project configuration
const firebaseConfig = {
    apiKey: "AIzaSyBxR7hT-I-XhA0zRD4y_fCfln7nj7bZAk0",
    authDomain: "the-projecr-faith.firebaseapp.com",
    projectId: "the-projecr-faith",
    storageBucket: "the-projecr-faith.firebasestorage.app",
    messagingSenderId: "747096151923",
    appId: "1:747096151923:web:bddb0a52a823548d955706",
    measurementId: "G-WCNYVF3H2Y"
};

// Initialize Firebase
let app, db;
let isFirebaseAvailable = false;

// Check if Firebase is available
if (typeof firebase !== 'undefined') {
    try {
        // Initialize Firebase
        app = firebase.initializeApp(firebaseConfig);
        db = firebase.firestore();
        isFirebaseAvailable = true;
        console.log('Firebase initialized successfully');
    } catch (error) {
        console.error('Error initializing Firebase:', error);
        isFirebaseAvailable = false;
    }
} else {
    console.warn('Firebase SDK not loaded. Using localStorage as fallback.');
    isFirebaseAvailable = false;
}

// Export configuration
window.firebaseConfig = {
    app,
    db,
    isFirebaseAvailable
};
