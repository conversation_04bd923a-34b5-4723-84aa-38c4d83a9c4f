// Database Manager - Handles both Firebase and localStorage
class DatabaseManager {
    constructor() {
        this.isFirebaseAvailable = window.firebaseConfig?.isFirebaseAvailable || false;
        this.db = window.firebaseConfig?.db;
        this.productsCollection = 'products';
        
        console.log('DatabaseManager initialized. Firebase available:', this.isFirebaseAvailable);
    }

    // Add a new product
    async addProduct(product) {
        try {
            if (this.isFirebaseAvailable) {
                // Add to Firebase
                const docRef = await this.db.collection(this.productsCollection).add({
                    ...product,
                    createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                    updatedAt: firebase.firestore.FieldValue.serverTimestamp()
                });
                
                // Update the product with the Firebase ID
                product.firebaseId = docRef.id;
                console.log('Product added to Firebase with ID:', docRef.id);
                
                // Also save to localStorage as backup
                this.addToLocalStorage(product);
                
                return { success: true, id: docRef.id };
            } else {
                // Fallback to localStorage
                return this.addToLocalStorage(product);
            }
        } catch (error) {
            console.error('Error adding product:', error);
            // Fallback to localStorage if Firebase fails
            return this.addToLocalStorage(product);
        }
    }

    // Get all products
    async getAllProducts() {
        try {
            if (this.isFirebaseAvailable) {
                const snapshot = await this.db.collection(this.productsCollection)
                    .orderBy('createdAt', 'desc')
                    .get();
                
                const products = [];
                snapshot.forEach(doc => {
                    products.push({
                        firebaseId: doc.id,
                        ...doc.data(),
                        id: doc.data().id || doc.id // Ensure we have an ID
                    });
                });
                
                console.log('Loaded', products.length, 'products from Firebase');
                
                // Also update localStorage as backup
                localStorage.setItem('vaith_products', JSON.stringify(products));
                
                return products;
            } else {
                // Fallback to localStorage
                return this.getFromLocalStorage();
            }
        } catch (error) {
            console.error('Error getting products from Firebase:', error);
            // Fallback to localStorage
            return this.getFromLocalStorage();
        }
    }

    // Update a product
    async updateProduct(productId, updates) {
        try {
            if (this.isFirebaseAvailable) {
                // Find the product by ID or firebaseId
                const snapshot = await this.db.collection(this.productsCollection)
                    .where('id', '==', productId)
                    .get();
                
                if (!snapshot.empty) {
                    const doc = snapshot.docs[0];
                    await doc.ref.update({
                        ...updates,
                        updatedAt: firebase.firestore.FieldValue.serverTimestamp()
                    });
                    
                    console.log('Product updated in Firebase');
                    
                    // Also update localStorage
                    this.updateInLocalStorage(productId, updates);
                    
                    return { success: true };
                } else {
                    throw new Error('Product not found');
                }
            } else {
                // Fallback to localStorage
                return this.updateInLocalStorage(productId, updates);
            }
        } catch (error) {
            console.error('Error updating product:', error);
            // Fallback to localStorage
            return this.updateInLocalStorage(productId, updates);
        }
    }

    // Delete a product
    async deleteProduct(productId) {
        try {
            if (this.isFirebaseAvailable) {
                // Find and delete from Firebase
                const snapshot = await this.db.collection(this.productsCollection)
                    .where('id', '==', productId)
                    .get();
                
                if (!snapshot.empty) {
                    const doc = snapshot.docs[0];
                    await doc.ref.delete();
                    
                    console.log('Product deleted from Firebase');
                    
                    // Also delete from localStorage
                    this.deleteFromLocalStorage(productId);
                    
                    return { success: true };
                } else {
                    throw new Error('Product not found');
                }
            } else {
                // Fallback to localStorage
                return this.deleteFromLocalStorage(productId);
            }
        } catch (error) {
            console.error('Error deleting product:', error);
            // Fallback to localStorage
            return this.deleteFromLocalStorage(productId);
        }
    }

    // Listen for real-time updates
    onProductsChange(callback) {
        if (this.isFirebaseAvailable) {
            return this.db.collection(this.productsCollection)
                .orderBy('createdAt', 'desc')
                .onSnapshot(snapshot => {
                    const products = [];
                    snapshot.forEach(doc => {
                        products.push({
                            firebaseId: doc.id,
                            ...doc.data(),
                            id: doc.data().id || doc.id
                        });
                    });
                    
                    // Update localStorage backup
                    localStorage.setItem('vaith_products', JSON.stringify(products));
                    
                    callback(products);
                }, error => {
                    console.error('Error listening to products:', error);
                    // Fallback to localStorage
                    callback(this.getFromLocalStorage());
                });
        } else {
            // For localStorage, we can't listen for changes from other tabs
            // But we can check periodically or use storage events
            window.addEventListener('storage', (e) => {
                if (e.key === 'vaith_products') {
                    callback(this.getFromLocalStorage());
                }
            });
            
            // Initial call
            callback(this.getFromLocalStorage());
            
            return () => {}; // Return empty unsubscribe function
        }
    }

    // LocalStorage helper methods
    addToLocalStorage(product) {
        try {
            const products = this.getFromLocalStorage();
            products.push(product);
            localStorage.setItem('vaith_products', JSON.stringify(products));
            console.log('Product added to localStorage');
            return { success: true, id: product.id };
        } catch (error) {
            console.error('Error adding to localStorage:', error);
            return { success: false, error: error.message };
        }
    }

    getFromLocalStorage() {
        try {
            const products = localStorage.getItem('vaith_products');
            return products ? JSON.parse(products) : [];
        } catch (error) {
            console.error('Error reading from localStorage:', error);
            return [];
        }
    }

    updateInLocalStorage(productId, updates) {
        try {
            const products = this.getFromLocalStorage();
            const index = products.findIndex(p => p.id == productId);
            
            if (index !== -1) {
                products[index] = { ...products[index], ...updates };
                localStorage.setItem('vaith_products', JSON.stringify(products));
                console.log('Product updated in localStorage');
                return { success: true };
            } else {
                throw new Error('Product not found in localStorage');
            }
        } catch (error) {
            console.error('Error updating in localStorage:', error);
            return { success: false, error: error.message };
        }
    }

    deleteFromLocalStorage(productId) {
        try {
            const products = this.getFromLocalStorage();
            const filteredProducts = products.filter(p => p.id != productId);
            localStorage.setItem('vaith_products', JSON.stringify(filteredProducts));
            console.log('Product deleted from localStorage');
            return { success: true };
        } catch (error) {
            console.error('Error deleting from localStorage:', error);
            return { success: false, error: error.message };
        }
    }

    // Check connection status
    isOnline() {
        return navigator.onLine && this.isFirebaseAvailable;
    }

    // Sync localStorage with Firebase (useful for offline/online scenarios)
    async syncWithFirebase() {
        if (!this.isFirebaseAvailable) return;
        
        try {
            const localProducts = this.getFromLocalStorage();
            const firebaseProducts = await this.getAllProducts();
            
            // This is a simple sync - in a real app you'd want more sophisticated conflict resolution
            console.log('Sync completed. Firebase has', firebaseProducts.length, 'products');
        } catch (error) {
            console.error('Error syncing with Firebase:', error);
        }
    }
}

// Create global instance
window.databaseManager = new DatabaseManager();
